<execution>
  <constraint>
    ## 技术约束条件
    - **SVG标准兼容性**：必须符合W3C SVG 1.1/2.0标准规范
    - **浏览器兼容性**：支持主流浏览器（Chrome, Firefox, Safari, Edge）
    - **文件大小限制**：单个SVG文件不超过2MB，确保加载性能
    - **分辨率要求**：支持高DPI显示，矢量图形无失真缩放
    - **光学术语准确性**：所有专业术语必须准确无误
    - **色彩可访问性**：考虑色盲用户，提供足够的对比度
  </constraint>

  <rule>
    ## 强制执行规则
    - **图形元素标准化**：使用统一的形状、颜色、字体规范
    - **数据流向清晰性**：所有连接线必须有明确的方向指示
    - **文本可读性**：最小字体大小12px，确保清晰可读
    - **层次结构明确**：使用z-index和分组确保元素层次清晰
    - **命名规范性**：所有元素ID和class使用语义化命名
    - **代码整洁性**：SVG代码结构清晰，便于维护和修改
  </rule>

  <guideline>
    ## 设计指导原则
    - **专业美学**：追求技术图表的专业性和美观性平衡
    - **信息层次**：通过视觉权重引导用户阅读路径
    - **一致性原则**：同类元素使用相同的视觉样式
    - **简洁性原则**：避免不必要的装饰，突出核心信息
    - **可扩展性**：设计支持后续内容的添加和修改
    - **用户友好**：考虑不同背景用户的理解需求
  </guideline>

  <process>
    ## SVG创建标准流程
    
    ### Step 1: 内容分析与规划 (15分钟)
    ```mermaid
    flowchart TD
        A[接收原始内容] --> B[分析技术要素]
        B --> C[识别信息层次]
        C --> D[确定视觉目标]
        D --> E[制定创作计划]
    ```
    
    **具体操作**：
    - 提取关键技术概念和流程步骤
    - 识别数据流向和依赖关系
    - 确定目标受众和使用场景
    - 规划画布尺寸和布局方案
    
    ### Step 2: 结构设计与布局 (20分钟)
    ```mermaid
    graph LR
        A[画布规划] --> B[区域划分]
        B --> C[元素定位]
        C --> D[连接设计]
        D --> E[样式规范]
    ```
    
    **布局策略**：
    - **四象限布局**：适用于四阶段流程图
    - **时间轴布局**：适用于序列化流程
    - **层次树布局**：适用于分层架构图
    - **网格布局**：适用于复杂关系图
    
    ### Step 3: SVG代码实现 (30分钟)
    ```xml
    <!-- SVG基础结构模板 -->
    <svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <!-- 定义样式和渐变 -->
        <style>
          .stage-1 { fill: #e6f3ff; stroke: #333; }
          .stage-2 { fill: #e6ffe6; stroke: #333; }
          .stage-3 { fill: #fff5e6; stroke: #333; }
          .stage-4 { fill: #f2e6ff; stroke: #333; }
        </style>
      </defs>
      
      <!-- 分组绘制各个阶段 -->
      <g id="stage-1">
        <!-- 阶段一内容 -->
      </g>
      <!-- 其他阶段... -->
    </svg>
    ```
    
    ### Step 4: 质量检查与优化 (15分钟)
    ```mermaid
    flowchart TD
        A[代码检查] --> B[视觉测试]
        B --> C[兼容性验证]
        C --> D[性能优化]
        D --> E[最终交付]
    ```
    
    **检查清单**：
    - ✅ SVG语法正确性
    - ✅ 视觉效果一致性
    - ✅ 文本清晰可读性
    - ✅ 颜色对比度充足
    - ✅ 文件大小合理性
    - ✅ 跨平台兼容性
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 技术准确性 (40%)
    - ✅ 光学术语使用准确
    - ✅ 流程逻辑表达正确
    - ✅ 数据关系描述清晰
    - ✅ 技术细节无遗漏
    
    ### 视觉效果 (30%)
    - ✅ 整体布局协调美观
    - ✅ 色彩搭配专业合理
    - ✅ 字体选择清晰易读
    - ✅ 元素比例恰当平衡
    
    ### 用户体验 (20%)
    - ✅ 信息层次清晰明确
    - ✅ 阅读路径引导自然
    - ✅ 交互反馈及时准确
    - ✅ 多设备显示适配良好
    
    ### 技术规范 (10%)
    - ✅ SVG代码结构规范
    - ✅ 文件大小控制合理
    - ✅ 浏览器兼容性良好
    - ✅ 可维护性和扩展性强
  </criteria>
</execution>
