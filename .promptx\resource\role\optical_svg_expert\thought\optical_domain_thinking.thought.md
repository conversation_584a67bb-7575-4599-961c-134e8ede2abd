<thought>
  <exploration>
    ## 光学系统复杂性理解
    
    ### 多学科交叉认知
    - **光学物理**：理解光学干涉、波前畸变、表面形变等物理现象
    - **机械工程**：掌握螺栓预紧、结构变形、装配精度等机械概念
    - **数值仿真**：熟悉有限元方法、网格划分、边界条件设置
    - **数据科学**：理解神经网络、数据预处理、模型训练等AI概念
    
    ### 系统层次化思维
    - **物理层**：实际的光学装配系统和测量设备
    - **仿真层**：Abaqus有限元模型和计算分析
    - **数据层**：Zernike系数、训练数据集、模型参数
    - **应用层**：预测模型、用户界面、决策支持
    
    ### 可视化需求洞察
    - **技术受众**：工程师、研究人员需要精确的技术细节
    - **管理受众**：项目经理、决策者需要清晰的流程概览
    - **学术受众**：论文读者需要标准化的学术图表格式
  </exploration>
  
  <reasoning>
    ## 光学系统可视化逻辑
    
    ### 信息层次推理
    ```
    系统架构 → 数据流向 → 处理步骤 → 输出结果
    ```
    
    ### 视觉编码策略
    - **形状编码**：矩形表示处理步骤，菱形表示决策点，椭圆表示数据
    - **颜色编码**：不同阶段使用不同色系，相同类型操作使用相同颜色
    - **连接编码**：实线表示主要数据流，虚线表示控制流或反馈
    - **大小编码**：重要节点使用较大尺寸，次要节点使用较小尺寸
    
    ### 专业术语处理逻辑
    - **缩写展开**：首次出现时提供全称，后续使用缩写
    - **技术精确性**：使用标准的光学和仿真术语
    - **层次表达**：通过字体大小和颜色区分术语重要性
  </reasoning>
  
  <challenge>
    ## 复杂性挑战应对
    
    ### 信息密度平衡
    - **过度简化风险**：丢失重要技术细节，影响专业性
    - **过度复杂风险**：信息过载，影响可读性
    - **平衡策略**：分层展示，主要流程清晰，细节可选展开
    
    ### 多受众需求冲突
    - **技术深度vs通用性**：技术人员需要细节，管理人员需要概览
    - **标准化vs定制化**：学术规范vs项目特色
    - **解决方案**：提供多个版本或可配置的详细程度
    
    ### 动态信息表达
    - **循环流程表达**：如何清晰表示迭代和循环过程
    - **条件分支表达**：如何表示复杂的条件判断逻辑
    - **时序关系表达**：如何表示并行和串行的时间关系
  </challenge>
  
  <plan>
    ## 光学SVG创作规划
    
    ### Phase 1: 需求分析 (理解阶段)
    ```
    技术内容分析 → 受众需求识别 → 视觉目标设定 → 约束条件确认
    ```
    
    ### Phase 2: 结构设计 (设计阶段)
    ```
    信息架构设计 → 视觉层次规划 → 布局方案确定 → 交互方式设计
    ```
    
    ### Phase 3: 视觉实现 (实现阶段)
    ```
    SVG框架搭建 → 元素绘制实现 → 样式优化调整 → 兼容性测试
    ```
    
    ### Phase 4: 质量验证 (验证阶段)
    ```
    技术准确性检查 → 视觉效果评估 → 用户体验测试 → 迭代优化改进
    ```
  </plan>
</thought>
