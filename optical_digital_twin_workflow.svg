<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .stage-1 { fill: #e6f3ff; stroke: #1976d2; stroke-width: 2px; }
      .stage-2 { fill: #e6ffe6; stroke: #388e3c; stroke-width: 2px; }
      .stage-3 { fill: #fff5e6; stroke: #f57c00; stroke-width: 2px; }
      .stage-4 { fill: #f2e6ff; stroke: #7b1fa2; stroke-width: 2px; }
      .process-box { rx: 8; ry: 8; }
      .data-ellipse { rx: 60; ry: 25; }
      .decision-diamond { stroke-width: 2px; }
      .arrow { stroke: #333; stroke-width: 2px; fill: none; marker-end: url(#arrowhead); }
      .loop-arrow { stroke: #666; stroke-width: 2px; fill: none; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
      .stage-title { font-family: 'Arial', sans-serif; font-size: 16px; font-weight: bold; fill: #333; }
      .process-text { font-family: 'Arial', sans-serif; font-size: 12px; fill: #333; text-anchor: middle; }
      .file-text { font-family: 'Arial', sans-serif; font-size: 11px; fill: #555; text-anchor: middle; font-style: italic; }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    
    <linearGradient id="stage1Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e6f3ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="stage2Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e6ffe6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="stage3Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff5e6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffe0b2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="stage4Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f2e6ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e1bee7;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Stage 1: Data Generation and Extraction -->
  <g id="stage-1">
    <rect x="20" y="20" width="320" height="220" class="stage-1 process-box" fill="url(#stage1Gradient)" />
    <text x="180" y="40" class="stage-title">阶段一: 数据生成与提取</text>
    
    <!-- Start node -->
    <circle cx="80" cy="70" r="15" fill="#4caf50" stroke="#2e7d32" stroke-width="2"/>
    <text x="80" y="75" class="process-text" style="font-size: 10px;">开始</text>
    
    <!-- Abaqus CAE Model -->
    <ellipse cx="180" cy="100" class="data-ellipse" fill="#fff" stroke="#1976d2" stroke-width="2"/>
    <text x="180" y="95" class="file-text">Abaqus CAE</text>
    <text x="180" y="107" class="file-text">模型文件</text>
    
    <!-- Primary processing -->
    <rect x="100" y="130" width="160" height="40" class="process-box" fill="#fff" stroke="#1976d2" stroke-width="2"/>
    <text x="180" y="145" class="process-text">执行 abaqus_Primary_processing.py</text>
    <text x="180" y="158" class="process-text">(有限元批量仿真)</text>
    
    <!-- ODB files -->
    <ellipse cx="180" cy="200" class="data-ellipse" fill="#fff" stroke="#1976d2" stroke-width="2"/>
    <text x="180" y="195" class="file-text">生成一系列</text>
    <text x="180" y="207" class="file-text">.odb 结果文件</text>
    
    <!-- Loop arrow for different preload forces -->
    <path d="M 260 150 Q 300 150 300 130 Q 300 110 260 130" class="loop-arrow"/>
    <text x="285" y="125" class="file-text" style="font-size: 10px;">循环施加不同预紧力</text>
  </g>
  
  <!-- Stage 2: Dataset Construction and Preprocessing -->
  <g id="stage-2">
    <rect x="360" y="20" width="320" height="220" class="stage-2 process-box" fill="url(#stage2Gradient)" />
    <text x="520" y="40" class="stage-title">阶段二: 数据集构建与预处理</text>
    
    <!-- Zernike calculation -->
    <rect x="380" y="70" width="160" height="40" class="process-box" fill="#fff" stroke="#388e3c" stroke-width="2"/>
    <text x="460" y="85" class="process-text">执行 surface_zernike_calculation.py</text>
    <text x="460" y="98" class="process-text">(泽尼克系数计算)</text>
    
    <!-- JSON files -->
    <ellipse cx="460" cy="140" class="data-ellipse" fill="#fff" stroke="#388e3c" stroke-width="2"/>
    <text x="460" y="135" class="file-text">生成一系列</text>
    <text x="460" y="147" class="file-text">.json 数据文件</text>
    
    <!-- Data processor -->
    <rect x="380" y="170" width="160" height="40" class="process-box" fill="#fff" stroke="#388e3c" stroke-width="2"/>
    <text x="460" y="185" class="process-text">执行 zernike_data_processor.py</text>
    <text x="460" y="198" class="process-text">(数据整合)</text>
    
    <!-- Dataset CSV -->
    <ellipse cx="520" cy="220" class="data-ellipse" fill="#fff" stroke="#388e3c" stroke-width="2"/>
    <text x="520" y="215" class="file-text">整合后的数据集</text>
    <text x="520" y="227" class="file-text">_zernike_dataset.csv</text>
    
    <!-- Loop arrow for processing ODB files -->
    <path d="M 540 90 Q 580 90 580 70 Q 580 50 540 70" class="loop-arrow"/>
    <text x="565" y="65" class="file-text" style="font-size: 10px;">逐个处理.odb文件</text>
  </g>

  <!-- Stage 3: Neural Network Model Training -->
  <g id="stage-3">
    <rect x="700" y="20" width="320" height="280" class="stage-3 process-box" fill="url(#stage3Gradient)" />
    <text x="860" y="40" class="stage-title">阶段三: 神经网络模型训练</text>

    <!-- Data loading -->
    <rect x="720" y="60" width="160" height="30" class="process-box" fill="#fff" stroke="#f57c00" stroke-width="2"/>
    <text x="800" y="78" class="process-text">执行 neural_network.py 中的 load_data</text>

    <!-- Data preprocessing steps -->
    <rect x="720" y="100" width="160" height="25" class="process-box" fill="#fff" stroke="#f57c00" stroke-width="2"/>
    <text x="800" y="115" class="process-text">1. 分离特征与标签</text>

    <rect x="720" y="135" width="160" height="25" class="process-box" fill="#fff" stroke="#f57c00" stroke-width="2"/>
    <text x="800" y="150" class="process-text">2. Z-score 标准化</text>

    <rect x="720" y="170" width="160" height="25" class="process-box" fill="#fff" stroke="#f57c00" stroke-width="2"/>
    <text x="800" y="185" class="process-text">3. 划分数据集 (80%/20%)</text>

    <!-- Model architecture -->
    <rect x="720" y="210" width="160" height="30" class="process-box" fill="#fff" stroke="#f57c00" stroke-width="2"/>
    <text x="800" y="228" class="process-text">定义 BoltForceNet 模型架构</text>

    <!-- Training loop -->
    <polygon points="890,250 920,265 890,280 860,265" fill="#fff" stroke="#f57c00" stroke-width="2"/>
    <text x="890" y="268" class="process-text" style="font-size: 11px;">训练循环</text>

    <!-- Model saving -->
    <ellipse cx="800" cy="290" class="data-ellipse" fill="#fff" stroke="#f57c00" stroke-width="2"/>
    <text x="800" y="285" class="file-text">训练好的模型文件</text>
    <text x="800" y="297" class="file-text">.pth (包含权重和参数)</text>

    <!-- Training loop arrow -->
    <path d="M 920 265 Q 960 265 960 245 Q 960 225 920 245" class="loop-arrow"/>
    <text x="945" y="240" class="file-text" style="font-size: 10px;">Epochs循环</text>
  </g>

  <!-- Stage 4: Model Prediction and Application -->
  <g id="stage-4">
    <rect x="1040" y="20" width="320" height="220" class="stage-4 process-box" fill="url(#stage4Gradient)" />
    <text x="1200" y="40" class="stage-title">阶段四: 模型预测与应用</text>

    <!-- Load model -->
    <rect x="1060" y="70" width="160" height="30" class="process-box" fill="#fff" stroke="#7b1fa2" stroke-width="2"/>
    <text x="1140" y="88" class="process-text">执行 nn_prediction.py (加载模型)</text>

    <!-- Input data -->
    <ellipse cx="1140" cy="130" class="data-ellipse" fill="#fff" stroke="#7b1fa2" stroke-width="2"/>
    <text x="1140" y="125" class="file-text">输入新的</text>
    <text x="1140" y="137" class="file-text">螺栓预紧力值</text>

    <!-- Prediction process -->
    <rect x="1060" y="160" width="160" height="30" class="process-box" fill="#fff" stroke="#7b1fa2" stroke-width="2"/>
    <text x="1140" y="178" class="process-text">模型推理与反标准化</text>

    <!-- Output -->
    <ellipse cx="1140" cy="210" class="data-ellipse" fill="#fff" stroke="#7b1fa2" stroke-width="2"/>
    <text x="1140" y="205" class="file-text">输出预测的37个</text>
    <text x="1140" y="217" class="file-text">Zernike系数值</text>

    <!-- End node -->
    <circle cx="1280" cy="210" r="15" fill="#f44336" stroke="#c62828" stroke-width="2"/>
    <text x="1280" y="215" class="process-text" style="font-size: 10px;">结束</text>
  </g>

  <!-- Main flow arrows -->
  <!-- Stage 1 to Stage 2 -->
  <line x1="95" y1="70" x2="180" y2="100" class="arrow"/>
  <line x1="180" y1="125" x2="180" y2="130" class="arrow"/>
  <line x1="180" y1="170" x2="180" y2="185" class="arrow"/>
  <line x1="240" y1="200" x2="380" y2="90" class="arrow"/>

  <!-- Stage 2 internal flow -->
  <line x1="460" y1="110" x2="460" y2="125" class="arrow"/>
  <line x1="460" y1="155" x2="460" y2="170" class="arrow"/>
  <line x1="460" y1="210" x2="520" y2="205" class="arrow"/>

  <!-- Stage 2 to Stage 3 -->
  <line x1="580" y1="220" x2="720" y2="75" class="arrow"/>

  <!-- Stage 3 internal flow -->
  <line x1="800" y1="90" x2="800" y2="100" class="arrow"/>
  <line x1="800" y1="125" x2="800" y2="135" class="arrow"/>
  <line x1="800" y1="160" x2="800" y2="170" class="arrow"/>
  <line x1="800" y1="195" x2="800" y2="210" class="arrow"/>
  <line x1="800" y1="240" x2="860" y2="250" class="arrow"/>
  <line x1="890" y1="280" x2="800" y2="275" class="arrow"/>

  <!-- Stage 3 to Stage 4 -->
  <line x1="880" y1="290" x2="1060" y2="85" class="arrow"/>

  <!-- Stage 4 internal flow -->
  <line x1="1140" y1="100" x2="1140" y2="115" class="arrow"/>
  <line x1="1140" y1="145" x2="1140" y2="160" class="arrow"/>
  <line x1="1140" y1="190" x2="1140" y2="195" class="arrow"/>
  <line x1="1200" y1="210" x2="1265" y2="210" class="arrow"/>

</svg>
